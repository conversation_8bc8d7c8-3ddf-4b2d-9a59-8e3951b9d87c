#!/usr/bin/env python3

import sys
import os
import pathlib
import torch
import hydra
from omegaconf import OmegaConf
from termcolor import cprint

# Add current directory to path
sys.path.append('.')

def test_model_loading():
    """Test model loading and normalizer setup"""
    
    # Set paths
    data_path = "/home/<USER>/code/company/torqueidp3/data/raw_pour_converted"
    model_path = "/home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy/data/outputs/gr1_dex-3d-idp3-full_training_seed0/checkpoints/latest.ckpt"
    
    cprint("Testing model loading...", "yellow")
    cprint(f"Data path: {data_path}", "cyan")
    cprint(f"Model path: {model_path}", "cyan")
    
    # Check if files exist
    if not os.path.exists(data_path):
        cprint(f"Error: Data path not found: {data_path}", "red")
        return
    
    if not os.path.exists(model_path):
        cprint(f"Error: Model path not found: {model_path}", "red")
        return
    
    # Load config
    config_path = pathlib.Path(__file__).parent / "diffusion_policy_3d" / "config"
    
    with hydra.initialize(config_path=str(config_path), version_base=None):
        cfg = hydra.compose(config_name="idp3", overrides=[
            f"task.dataset.zarr_path={data_path}",
            "training.max_val_steps=1"
        ])
    
    cprint("Config loaded successfully", "green")
    
    # Create workspace
    cls = hydra.utils.get_class(cfg._target_)
    workspace = cls(cfg)
    
    cprint("Workspace created successfully", "green")
    
    # Load dataset
    dataset = hydra.utils.instantiate(cfg.task.dataset)
    cprint(f"Dataset loaded: {len(dataset)} samples", "green")
    
    # Get normalizer
    normalizer = dataset.get_normalizer()
    cprint("Normalizer created", "green")
    cprint(f"Normalizer keys: {list(normalizer.keys())}", "cyan")
    
    # Load model
    workspace.load_checkpoint(path=model_path)
    policy = workspace.get_model()
    policy.eval()
    
    cprint("Model loaded successfully", "green")
    
    # Set normalizer
    policy.set_normalizer(normalizer)
    cprint("Normalizer set to policy", "green")
    
    # Test with a small batch
    val_dataset = dataset.get_validation_dataset()
    if len(val_dataset) > 0:
        sample = val_dataset[0]
        obs_dict = {k: v.unsqueeze(0) for k, v in sample['obs'].items()}
        
        cprint("Testing prediction...", "yellow")
        with torch.no_grad():
            try:
                result = policy.predict_action(obs_dict)
                pred_action = result['action_pred']
                cprint(f"Prediction successful! Shape: {pred_action.shape}", "green")
                
                # Test unnormalization
                pred_action_real = normalizer['action'].unnormalize(pred_action)
                cprint(f"Unnormalization successful! Shape: {pred_action_real.shape}", "green")
                
            except Exception as e:
                cprint(f"Prediction failed: {e}", "red")
                import traceback
                traceback.print_exc()
    else:
        cprint("No validation data available", "yellow")

if __name__ == "__main__":
    test_model_loading()
