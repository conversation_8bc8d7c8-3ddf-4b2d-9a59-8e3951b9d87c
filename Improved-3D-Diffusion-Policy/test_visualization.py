#!/usr/bin/env python3

import numpy as np
import matplotlib.pyplot as plt
import pathlib
from termcolor import cprint

# Import the visualization functions from eval_joint_angles
import sys
sys.path.append('.')
from eval_joint_angles import (
    plot_step_aligned_predictions, 
    plot_prediction_vs_gt, 
    plot_multiple_samples_comparison,
    JOINT_NAMES
)

def create_test_data():
    """Create synthetic test data to demonstrate the visualization"""
    N = 5  # Number of samples
    T = 10  # Number of time steps
    action_dim = 25  # Number of joints
    
    # Create synthetic ground truth data (smooth trajectories)
    gt_actions = np.zeros((N, T, action_dim))
    for n in range(N):
        for j in range(action_dim):
            # Create smooth sinusoidal trajectories with different frequencies and phases
            freq = 0.1 + j * 0.05
            phase = n * 0.2 + j * 0.1
            amplitude = 0.5 + j * 0.1
            gt_actions[n, :, j] = amplitude * np.sin(2 * np.pi * freq * np.arange(T) + phase)
    
    # Create synthetic prediction data (GT + noise + bias)
    pred_actions = gt_actions.copy()
    
    # Add some systematic bias and noise
    for n in range(N):
        for t in range(T):
            for j in range(action_dim):
                # Add time-dependent bias
                bias = 0.1 * np.sin(0.3 * t) * (j % 3 - 1)
                # Add random noise
                noise = np.random.normal(0, 0.05)
                # Add step-dependent error (later steps have more error)
                step_error = 0.02 * t * np.random.normal(0, 1)
                
                pred_actions[n, t, j] += bias + noise + step_error
    
    return pred_actions, gt_actions

def test_visualizations():
    """Test all visualization functions with synthetic data"""
    cprint("Creating synthetic test data...", "yellow")
    pred_actions, gt_actions = create_test_data()
    
    N, T, action_dim = pred_actions.shape
    cprint(f"Test data shape: N={N}, T={T}, action_dim={action_dim}", "cyan")
    
    # Create output directory
    output_dir = pathlib.Path("./test_visualization_output")
    output_dir.mkdir(exist_ok=True)
    
    cprint("Testing step-aligned predictions visualization...", "yellow")
    plot_step_aligned_predictions(pred_actions, gt_actions, JOINT_NAMES, output_dir, 
                                sample_idx=0, joint_indices=[0, 3, 8, 13])
    
    cprint("Testing prediction vs GT visualization...", "yellow")
    plot_prediction_vs_gt(pred_actions, gt_actions, JOINT_NAMES, output_dir, 
                         sample_idx=0, joint_indices=list(range(6)))
    
    cprint("Testing multiple samples comparison...", "yellow")
    plot_multiple_samples_comparison(pred_actions, gt_actions, JOINT_NAMES, output_dir, 
                                   joint_idx=0, max_samples=3)
    
    cprint(f"All test visualizations saved to: {output_dir}", "green")
    cprint("You can now run the actual evaluation script on your trained model!", "green")

if __name__ == "__main__":
    test_visualizations()
