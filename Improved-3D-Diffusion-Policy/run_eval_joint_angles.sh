#!/bin/bash

# 设置工作目录
cd /home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy

# 设置环境变量
export PYTHONPATH=/home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy:$PYTHONPATH

# 运行关节角度评估
echo "Starting joint angle evaluation..."
echo "Data path: /home/<USER>/code/company/torqueidp3/data/raw_pour_converted"
echo "Model path: /home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy/data/outputs/gr1_dex-3d-idp3-full_training_seed0/checkpoints/latest.ckpt"
echo ""

python eval_joint_angles.py \
    --config-name=eval_pour \
    checkpoint_path=/home/<USER>/code/company/torqueidp3/Improved-3D-Diffusion-Policy/data/outputs/gr1_dex-3d-idp3-full_training_seed0/checkpoints/latest.ckpt \
    hydra.run.dir=./eval_outputs/joint_angles_$(date +%Y%m%d_%H%M%S)

echo ""
echo "Evaluation completed! Check the output directory for results and visualizations."
